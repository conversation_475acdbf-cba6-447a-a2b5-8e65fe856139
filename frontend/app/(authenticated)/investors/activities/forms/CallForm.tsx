import { CallFormData, CallFormData as InstagramFormSchema } from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';

interface CallFormProps {
    formData: CallFormData;
    setFormData: (data: CallFormData) => void;
}

export default function CallForm({ formData, setFormData }: CallFormProps) {
    const { getFieldError, validateField } = useFormValidation(
        InstagramFormSchema,
        formData
    );

    const updateField = useCallback(
        (field: keyof CallFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof CallFormData) => {
            validateField(field);
        },
        [validateField]
    );

    return (
        <div className="space-y-4 mt-4">
            <TextArea
                label="Post Content"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://www.example.com/..."
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                onBlur={() => handleBlur('image')}
                placeholder="https://example.com/image.jpg"
                error={getFieldError('image')}
            />
        </div>
    );
}
