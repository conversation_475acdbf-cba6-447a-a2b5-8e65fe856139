import { Popover, PopoverButton, PopoverPanel, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import classNames from 'classnames';
import ActivityFormatIndicator from '@/components/ActivityFormatIndicator';

type FilterMenuProps<T> = {
    menuName: string;
    items: string[];
    selectedItems: string[];
    onChange: (selected: string[]) => void;
    itemToString?: (item: T) => string;
    className?: string;
};

export default function FilterMenu<T>({
    menuName,
    items,
    selectedItems,
    onChange,
    className,
}: FilterMenuProps<T>) {
    function toggleItem(item: string) {
        if (selectedItems.includes(item)) {
            onChange(selectedItems.filter(i => i !== item));
        } else {
            onChange([...selectedItems, item]);
        }
    }

    function getLabel(item: string) {
        switch (menuName) {
            case 'Format':
                return <ActivityFormatIndicator activity={item} />;
            default:
                return item;
        }
    }

    return (
        <Popover className="relative">
            {({ open }) => (
                <>
                    <PopoverButton
                        className={classNames(
                            "py-1 px-2 w-[288px] text-left text-sm font-medium leading-6 text-gray-900 hover:bg-gray-100",
                            "truncate overflow-hidden whitespace-nowrap",
                            className
                        )}
                    >
                        {menuName} {selectedItems.length ? `(${selectedItems.join(', ')})` : ''}
                    </PopoverButton>

                    <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                    >
                        <PopoverPanel
                            className="absolute top-0 left-full ml-0 z-10 w-48 rounded-md bg-white shadow-lg ring-1 py-2 ring-black ring-opacity-5 focus:outline-none max-h-60 overflow-auto"
                        >
                            {items.map(item => {
                                const label = item;
                                const selected = selectedItems.includes(item);
                                return (
                                    <button
                                        key={label}
                                        type="button"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toggleItem(item);
                                        }}
                                        className={classNames(
                                            "flex items-center w-full px-2 py-1 text-sm cursor-pointer",
                                            selected ? "bg-indigo-600 text-white" : "text-gray-900 hover:bg-gray-100"
                                        )}
                                    >
                                        {selected && <CheckIcon className="h-5 w-5 mr-1" aria-hidden="true" />}
                                        {getLabel(item)}
                                    </button>
                                );
                            })}
                        </PopoverPanel>
                    </Transition>
                </>
            )}
        </Popover>
    );
}


