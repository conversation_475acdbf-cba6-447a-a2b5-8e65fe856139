import { isBroadcast } from '@/api/hooks/useBroadcasts';
import SentimentIndicator from '@/app/(authenticated)/investors/activities/SentimentIndicator';
import ActivityFormatIndicator from '@/components/ActivityFormatIndicator';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import Checkbox from '@/components/ui/Checkbox';
import { formatWithTimeZone } from '@/util/date';
import useActivitySummary from '@/util/useActivitySummary';
import { ArrowTurnDownRightIcon } from '@heroicons/react/20/solid';
import { Activity } from '@quarterback/types';
import classNames from 'classnames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';
import { useActivitiesLayout } from './ActivityLayoutContext';
import activityFormat from '@/util/activityFormat';
import { FlagIcon } from '@heroicons/react/24/outline';

export default function ActivityRow({
    activity,
    className,
    selectedActivities,
    setSelectedActivities
}: {
    activity: Activity;
    className?: string;
    selectedActivities: Array<string>;
    setSelectedActivities: React.Dispatch<React.SetStateAction<string[]>>;
}) {
    const pathname = usePathname();
    const searchParams = useSearchParams();

    const { setIsRightPanelExpanded } = useActivitiesLayout();
    const router = useRouter();
    const { title, description, author, engagement } = useActivitySummary(activity);

    const broadcast = useMemo(() => {
        return isBroadcast(activity);
    }, [activity]);

    const truncatedAuthor = useMemo(() => {
        if (author === 'Australian Securities Exchange') return 'ASX';
        else if (author && (author?.length ?? 0) > 25) {
            return author.substring(0, 25) + '…';
        } else {
            return author;
        }
    }, [author]);

    function handleClick() {
        setIsRightPanelExpanded(true);
        router.push(
            `/investors/activities/${activity.id}${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`
        );
    }

    const showThreadIcon = useMemo(() => {
        return activity.type === 'hotcopper' && activity.url.includes('page-');
    }, [activity]);

    const priceSensitive = useMemo(() => {
        if (activity.type === 'asx-announcement' && activity.priceSensitive)
            return <span className="text-red-500">$</span>;
        return null;
    }, [activity]);

    const handleCheckboxClick = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation();
        setSelectedActivities((prev) => {
            if (e.target.checked) {
                return activity.id ? [...prev, activity.id] : prev;
            } else {
                return prev.filter((id) => id !== activity.id);
            }
        });
    };

    const activeRow = pathname === `/investors/activities/${activity.id}`;

    return (
        <tr
            key={activity.id}
            className={classNames(
                className,
                'cursor-pointer group text-sm font-normal text-qb-black-100 bg-qb-gray-30 hover:bg-qb-gray-125',
                {
                    'bg-qb-gray-125': activeRow
                }
            )}
            onClick={handleClick}>
            <td className={classNames('whitespace-nowrap px-2 bg-white')}>
                <Checkbox
                    onClick={(e) => e.stopPropagation()}
                    onChange={handleCheckboxClick}
                    checked={
                        activity.id ? selectedActivities.includes(activity.id) : false
                    }
                />
            </td>
            <td
                className={classNames(
                    'whitespace-nowrap px-2 pr-6 bg-white hover:bg-qb-gray-125',
                    {
                        'bg-qb-gray-125': activeRow
                    }
                )}>
                <div className="flex items-center gap-x-2">
                    <ActivitySourceIcon
                        className="rounded-md size-5 object-cover"
                        activity={activity}
                    />
                    <span className="flex-1 hover:underline hover:cursor-pointer">
                        {truncatedAuthor}
                    </span>
                    {activity.flagged ? (
                        <FlagIcon className="size-4 text-red-600 justify-self-end" />
                    ) : null}
                </div>
            </td>

            <td className="px-4 overflow-hidden break-all text-ellipsis border border-qb-gray-115 text-qb-gray-150">
                <div className=" flex items-center gap-x-2">
                    {showThreadIcon ? (
                        <ArrowTurnDownRightIcon className="size-5" />
                    ) : null}
                    {title ? (
                        <>
                            <span className="text-ellipsis overflow-hidden line-clamp-1 break-all">
                                {title}
                            </span>
                            {priceSensitive}
                        </>
                    ) : null}
                    {description ? (
                        <span className="mt-1 flex-1 text-ellipsis overflow-hidden line-clamp-1 break-all">
                            {description}
                        </span>
                    ) : null}
                </div>
            </td>
            <td className="whitespace-nowrap py-3 sm:pl-3 border border-qb-gray-115">
                {formatWithTimeZone(activity.posted, 'HH:mm')}
            </td>
            <td className="relative whitespace-nowrap text-right sm:pr-3 border border-qb-gray-115">
                {activity.sentiment && (
                    <SentimentIndicator sentiment={activity.sentiment} />
                )}
            </td>
            <td className="whitespace-nowrap py-2 px-5 text-right border border-qb-gray-115">
                {<ActivityFormatIndicator activity={activityFormat(activity)} />}
            </td>
        </tr>
    );
}
