'use client';

import React, { useState } from 'react';
import { useOrganisation } from '@/components/OrganisationProvider';
import useAlertRules from '@/api/hooks/useAlertRules';
import AlertRuleRow from '@/app/(authenticated)/alerts/rules/AlertRuleRow';
import {
    Bars3BottomLeftIcon,
    BarsArrowDownIcon,
    BellIcon,
    ClockIcon,
    PlusIcon,
    UserIcon
} from '@heroicons/react/24/outline';
import Dropdown, { DropdownOption } from '@/components/ui/Dropdown';
import AlertForm from '../_form/AlertForm';
import Button from '@/components/ui/Button';

const options: DropdownOption[] = [
    { name: 'Creation Date', value: 'created' },
    { name: 'Name', value: 'name' }
];
export default function AlertRulesPage() {
    const [selectedSort, setSelectedSort] = useState(options[0].value);
    const [popupOpen, setPopupOpen] = useState(false);

    const organisation = useOrganisation();
    const { data: alerts = [] } = useAlertRules(
        organisation?.selected?.organisation,
        organisation?.selected?.entity
    );

    return (
        <>
            <div className="border-b border-gray-200">
                <div className="flex justify-between px-4 py-2">
                    <div className="flex items-center">
                        <Dropdown
                            icon={BarsArrowDownIcon}
                            options={options}
                            selected={selectedSort}
                            onChange={setSelectedSort}
                            title="Sorted by"
                        />
                    </div>
                    <div className="flex flex-row items-center gap-x-1.5">
                        <Button
                            variant="primary"
                            size="sm"
                            onClick={() => setPopupOpen(true)}
                            icon={
                                <PlusIcon
                                    aria-hidden="true"
                                    className="-ml-0.5 h-5 w-5"
                                />
                            }>
                            New Notification
                        </Button>
                    </div>
                </div>
            </div>
            {alerts.length ? (
                <>
                    {' '}
                    <table className="w-full bg-white table-auto relative">
                        <thead className="px-3 border-b border-gray-200">
                            <tr>
                                <th
                                    scope="colgroup"
                                    className="py-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                    Type
                                </th>
                                <th
                                    scope="colgroup"
                                    className="py-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                    <span className="flex items-center gap-1">
                                        <Bars3BottomLeftIcon className="h-4 w-4" />
                                        Description
                                    </span>
                                </th>
                                <th
                                    scope="colgroup"
                                    className="py-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                    <span className="flex items-center gap-1">
                                        <UserIcon className="h-4 w-4" />
                                        Created by
                                    </span>
                                </th>
                                <th
                                    scope="colgroup"
                                    className="py-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                    <span className="flex items-center gap-1">
                                        <ClockIcon className="h-4 w-4" />
                                        Created at
                                    </span>
                                </th>
                                <th
                                    scope="colgroup"
                                    className="py-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"></th>
                            </tr>{' '}
                        </thead>
                        <tbody className="bg-white">
                            <tr className="bg-gray-200">
                                <td
                                    scope="colgroup"
                                    colSpan={5}
                                    className="text-sm text-gray-600 px-3 py-2 border-b border-gray-200 ">
                                    <span className="flex items-center gap-2">
                                        Alerts
                                        <span className="bg-gray-300 ring-gray-700/10 px-1 rounded-sm ring-inset ring-1">
                                            {alerts.length}
                                        </span>
                                    </span>
                                </td>
                            </tr>
                            {alerts.map((alert) => (
                                <AlertRuleRow key={alert.id} alert={alert} />
                            ))}
                        </tbody>
                    </table>
                </>
            ) : (
                <div className="text-center my-16">
                    <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-semibold text-gray-900">
                        You don&rsquo;t have any alert rules
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                        Get started by creating a new alert rule.
                    </p>
                </div>
            )}
            <AlertForm setOpen={setPopupOpen} open={popupOpen} />
        </>
    );
}
