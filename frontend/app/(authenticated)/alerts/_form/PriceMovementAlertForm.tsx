import React, { SetStateAction } from 'react';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import ThresholdInput from './ThresholdInput';
import { AlertThreshold } from '../../../../../types/src/Alert';

export default function PriceMovementAlertForm({
    threshold,
    setThreshold
}: {
    threshold: AlertThreshold;
    setThreshold: React.Dispatch<SetStateAction<AlertThreshold>>;
}) {
    return (
        <>
            <div className='text-gray-500 text-sm mb-1'>Movement threshold</div>
            <div className='flex items-center gap-2'>
                <div className="inline-flex items-center border border-gray-200 rounded-lg bg-white overflow-hidden">

                    <span className="px-3 whitespace-nowrap ">When share price has moved</span>

                    <Popover as="span" className="relative py-1.5  bg-gray-100 border-l border-gray-200">
                        <PopoverButton
                            as={'span'}
                            className=" px-3 py-1.5 inline font-semibold underline text-indigo-700 cursor-pointer bg-gray-100 rounded-none">
                            {threshold?.threshold.toLocaleString()}%
                        </PopoverButton>
                        <PopoverPanel
                            anchor={{
                                to: 'bottom'
                            }}
                            className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                            {({ close }) => (
                                <ThresholdInput
                                    type="SHARE_PERCENT"
                                    close={close}
                                    threshold={threshold}
                                    setThreshold={setThreshold}
                                />
                            )}
                        </PopoverPanel>
                    </Popover>{' '}
                </div>
                <span>compared to price at open</span>
            </div>
        </>
    );
}
