import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React from 'react';
import ThresholdInput from './ThresholdInput';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';

const options = [
    { value: 'LTE', label: 'less than' },
    { value: 'GTE', label: 'more than' }
];

export default function SentimentAlertForm({
    threshold,
    setThreshold
}: {
    threshold: {
        comparator: 'GTE' | 'LTE';
        threshold: number;
    };
    setThreshold: React.Dispatch<
        React.SetStateAction<{ comparator: 'GTE' | 'LTE'; threshold: number }>
    >;
}) {
    return (
        <>
            <span className="text-gray-500 text-sm">Sentiment threhold</span>
            <div className="flex items-center gap-2">
                <div className="inline-flex items-center border border-gray-200 rounded-lg bg-white overflow-hidden">
                    <span className="px-3 whitespace-nowrap">Sentiment is</span>
                    <Listbox
                        value={threshold?.comparator}
                        onChange={(val: 'GTE' | 'LTE') =>
                            setThreshold({
                                ...threshold,
                                comparator: val
                            })
                        }>
                        {({ open }) => (
                            <div className="relative border-l border-gray-200">
                                <ListboxButton className="relative cursor-default rounded-r-md bg-gray-100 py-1.5 px-3 text-left border-none focus:ring-0 outline-none w-fit">
                                    {options.find(
                                        (opt) => opt.value === threshold?.comparator
                                    )?.label || (
                                        <span className="text-gray-500">Select</span>
                                    )}
                                </ListboxButton>

                                {open && (
                                    <div className="fixed z-50 w-[--button-width]">
                                        <ListboxOptions
                                            static
                                            className="max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none">
                                            {options.map((option) => (
                                                <ListboxOption
                                                    key={option.value}
                                                    value={option.value}
                                                    className={({ active }) =>
                                                        `relative cursor-default select-none py-2 px-4 ${
                                                            active ? 'bg-gray-100' : ''
                                                        }`
                                                    }>
                                                    {option.label}
                                                </ListboxOption>
                                            ))}
                                        </ListboxOptions>
                                    </div>
                                )}
                            </div>
                        )}
                    </Listbox>
                </div>
                <Popover as="span" className="relative">
                    <PopoverButton
                        as={'span'}
                        className="inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer border border-gray-200 rounded-md bg-gray-100 px-3 py-2 rounded-md">
                        {threshold?.threshold.toLocaleString()}
                    </PopoverButton>
                    <PopoverPanel
                        anchor={{
                            to: 'bottom'
                        }}
                        className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                        {({ close }) => (
                            <ThresholdInput
                                type="SENTIMENT"
                                close={close}
                                threshold={threshold}
                                setThreshold={setThreshold}
                                min={-1}
                                max={1}
                            />
                        )}
                    </PopoverPanel>
                </Popover>
            </div>
        </>
    );
}
