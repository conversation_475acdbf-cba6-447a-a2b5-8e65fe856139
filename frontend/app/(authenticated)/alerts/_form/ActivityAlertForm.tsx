import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React from 'react';
import ThresholdInput from './ThresholdInput';
import { ActivityAlertInterval } from '@quarterback/types';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';

const intervalOptions: { value: ActivityAlertInterval; label: string }[] = [
    { value: 'DAY', label: 'day' },
    { value: 'WEEK', label: 'week' },
    { value: 'MONTH', label: 'month' }
];

export default function ActivityAlertForm({
    threshold,
    setThreshold,
    interval,
    setInterval
}: {
    threshold: {
        comparator: 'GTE' | 'LTE';
        threshold: number;
    };
    setThreshold: React.Dispatch<
        React.SetStateAction<{ comparator: 'GTE' | 'LTE'; threshold: number }>
    >;
    interval: ActivityAlertInterval;
    setInterval: React.Dispatch<React.SetStateAction<ActivityAlertInterval>>;
}) {
    return (
        <>
            <span className="text-gray-500 text-sm mb-2">Keywords</span>
            <div className="flex items-center gap-2">
                <div className="inline-flex items-center border border-gray-200 rounded-lg bg-white overflow-hidden">
                    <span className="px-3 whitespace-nowrap">
                        Total activity count has changed by
                    </span>
                    <Popover
                        as="span"
                        className="relative py-1.5  bg-gray-100 border-l border-gray-100">
                        <PopoverButton
                            as={'span'}
                            className=" px-3 py-1.5 inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer bg-gray-100">
                            {threshold?.threshold.toLocaleString()}%
                        </PopoverButton>
                        <PopoverPanel
                            anchor={{
                                to: 'bottom'
                            }}
                            className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                            {({ close }) => (
                                <ThresholdInput
                                    type="ACTIVITY"
                                    close={close}
                                    threshold={threshold}
                                    setThreshold={setThreshold}
                                    min={0.01}
                                    max={100}
                                    step={0.01}
                                />
                            )}
                        </PopoverPanel>
                    </Popover>{' '}
                </div>
                compared to previous{' '}
                <Listbox value={interval} onChange={(val) => setInterval(val)}>
                    {({ open }) => (
                        <div className="relative">
                            <ListboxButton className="relative cursor-default rounded-md bg-gray-100 py-2 px-3 text-left border-none focus:ring-0 outline-none w-fit">
                                {intervalOptions.find((opt) => opt.value === interval!)
                                    ?.label || (
                                    <span className="text-gray-500">Select</span>
                                )}
                            </ListboxButton>

                            {open && (
                                <div className="fixed z-50 w-[--button-width]">
                                    <ListboxOptions
                                        static
                                        className="max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none">
                                        {intervalOptions.map((option) => (
                                            <ListboxOption
                                                key={option.value}
                                                value={option.value}
                                                className={({ active }) =>
                                                    `relative cursor-default select-none py-2 px-4 ${
                                                        active ? 'bg-gray-100' : ''
                                                    }`
                                                }>
                                                {option.label}
                                            </ListboxOption>
                                        ))}
                                    </ListboxOptions>
                                </div>
                            )}
                        </div>
                    )}
                </Listbox>
            </div>
        </>
    );
}
