import AlertFieldIcon from '@/app/(authenticated)/alerts/AlertFieldIcon';
import { <PERSON><PERSON><PERSON>ield } from '@quarterback/types';
import React from 'react';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';

export default function AlertTypePicker({
    setType,
    type
}: {
    type: AlertField | undefined;
    setType: React.Dispatch<React.SetStateAction<AlertField | undefined>>;
}) {
    const items: Array<{
        name: string;
        description: string;
        field: AlertField;
        disabled: boolean;
    }> = [
            {
                name: 'Price Movement',
                description:
                    'Alerts you to a change in your share price that may require attention.',
                field: 'SHARE_PERCENT',
                disabled: false
            },
            {
                name: 'Broad Search',
                description:
                    'Alerts you to conversations about broad topics or themes happening right now.',
                field: 'SEARCH',
                disabled: false
            },
            {
                name: 'Sentiment',
                description:
                    'Alerts you to any new activities that fall within certain sentiment parameters.',
                field: 'SENTIMENT',
                disabled: false
            },
            {
                name: 'Activity Level',
                description:
                    'Alerts when activity volume changes by type, category or across the board.',
                field: 'ACTIVITY',
                disabled: false
            }
        ];
    const selectedItem = items.find(item => item.field === type);


    return (
        <div className="my-2">
            <Listbox value={type}
                onChange={setType}>
                {({ open }) => (
                    <div className="relative">
                        <ListboxButton className="relative cursor-default rounded-lg bg-white py-1.5 pl-3 pr-10 text-left border bg-gray-100 border-gray-200 focus:outline-none">
                            {
                                type && selectedItem ?
                                    <div className="flex items-center gap-2">
                                        <AlertFieldIcon field={type} />
                                        {selectedItem.name}
                                    </div> : <span className='text-gray-500'>Select</span>
                            }
                        </ListboxButton>

                        {open && (
                            <div className="fixed z-50 w-[--button-width]">
                                <ListboxOptions
                                    static
                                    className="max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none"
                                >
                                    {items.map((option) => (
                                        <ListboxOption
                                            key={option.field}
                                            value={option.field}
                                            className={({ active }) =>
                                                `relative cursor-default select-none py-2 px-4 flex items-center gap-2 ${active ? 'bg-gray-100 ' : ''
                                                }`
                                            }
                                        >
                                            <AlertFieldIcon field={option.field} /> {option.name}
                                        </ListboxOption>
                                    ))}
                                </ListboxOptions>
                            </div>
                        )}
                    </div>
                )}
            </Listbox>
        </div>
    )

}
