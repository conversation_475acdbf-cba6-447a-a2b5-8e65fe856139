import React, { Fragment, useCallback, useMemo, useState } from 'react';
import { Description, Field, Label } from '@headlessui/react';
import * as z from 'zod';

function AlertEmailBadge({ email, remove }: { email: string; remove: () => void }) {
    return (
        <span className="inline-flex items-center gap-x-1 rounded-md bg-gray-100 px-2 py-1 ring-1 ring-inset ring-gray-500/10 text-indigo-700">
            {email}
            <button
                type="button"
                onClick={remove}
                className="group relative h-3.5 w-3.5 rounded-sm hover:bg-gray-500/20">
                <span className="sr-only">Remove</span>
                <svg
                    viewBox="0 0 14 14"
                    className="h-4 w-4 stroke-gray-600/50 group-hover:stroke-gray-600/75">
                    <path d="M4 4l6 6m0-6l-6 6" />
                </svg>
                <span className="absolute -inset-1" />
            </button>
        </span>
    );
}

const emailSchema = z.string().trim().min(1).email();

export default function AlertNotificationConfig({
    emails,
    setEmails
}: {
    emails: Array<string>;
    setEmails: React.Dispatch<React.SetStateAction<string[]>>;
}) {
    const [email, setEmail] = useState('');

    const addEmail = useCallback(() => {
        const parsed = emailSchema.safeParse(email);

        if (parsed.success) {
            setEmails([...emails, parsed.data]);
            setEmail('');
        }
    }, [setEmails, setEmail, emails, email]);

    const removeEmail = useCallback(
        (email: string) => {
            setEmails(emails.filter((it) => it !== email));
        },
        [setEmails, emails]
    );

    return (
        <div className='mt-2'>
            <span className='text-gray-500 text-sm'>
                Receive alert emails to
            </span>
            <div className="mt-4">
                {emails.length === 0 ? (
                    <div className="italic text-xs text-gray-500">
                        You haven&apos;t added any emails yet.
                    </div>
                ) : undefined}
                {emails.map((it, ix) => (
                    <Fragment key={ix}>
                        <AlertEmailBadge email={it} remove={() => removeEmail(it)} />{' '}
                    </Fragment>
                ))}
            </div>
            <div className="mt-4 flex rounded-md shadow-sm">
                <div className="relative flex flex-grow items-stretch focus-within:z-10">
                    <input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="e.g. <EMAIL>"
                        value={email}
                        onChange={(event) => setEmail(event.target.value)}
                        onKeyDown={(event) => {
                            if (event.key === 'Enter') {
                                addEmail();
                            }
                        }}
                        className="block w-full rounded-none rounded-l-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                </div>
                <button
                    type="button"
                    onClick={addEmail}
                    className="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    Add
                </button>
            </div>
        </div>
    );
}
