import classNames from 'classnames';
import { useMemo } from 'react';

export function ActivityFormatIndicator({ activity }: { activity: string }) {
    const [bgColor, textColor] = useMemo(() => {
        switch (activity) {
            case 'Media':
                return ['bg-red-100', 'text-red-600'];
            case 'Announcement':
                return ['bg-blue-100', 'text-blue-600'];
            case 'Broadcast':
                return ['bg-lime-100', 'text-lime-700'];
            case 'Chatter':
                return ['bg-yellow-100', 'text-yellow-700'];
            default:
                return ['bg-gray-200', 'text-gray-700'];
        }
    }, [activity]);

    return (
        <span
            className={classNames(
                bgColor,
                textColor,
                'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ring-gray-300'
            )}>
            {activity}
        </span>
    );
}

export default ActivityFormatIndicator;
