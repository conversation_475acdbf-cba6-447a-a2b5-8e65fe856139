import React, { useMemo } from 'react';
import ProfileMenu from '../navigation/ProfileMenu';
import { useUser } from '@/api/hooks/useUser';
import { usePathname } from 'next/navigation';
import { flatPages, pages, SectionItem, SidebarSections } from '@/util/pages';
import Initials from '../Initials';

type PageHeaderProps = {
    name?: string;
};

export default function PageHeader({ name }: PageHeaderProps) {
    const { data: user } = useUser();
    const { first_name: firstName, last_name: lastName } = user?.metadata ?? {};

    const location = usePathname();

    const currentPage = useMemo(() => {
        return flatPages.find((it) => location.startsWith(it?.href ?? '#'));
    }, [location]);

    const Icon = currentPage?.icon;

    const email = user?.emails?.[0];

    const initials = useMemo(() => {
        if (firstName && lastName) {
            return `${firstName[0]}${lastName[0]}`.toUpperCase();
        } else if (email) {
            return `${email[0]}${email[1]}`.toUpperCase();
        }
    }, [firstName, lastName, email]);

    return (
        <div className="sm:flex sm:items-center px-4 py-3 h-[60px] sticky top-0 bg-white border-b border-gray-300 z-10">
            <div className="flex gap-2 items-center flex-1 flex-row w-full">
                {Icon ? <Icon className="text-qb-gray-150 h-5 w-5 shrink-0" /> : null}
                {!Icon ? (
                    <Initials name={currentPage?.name} color={currentPage?.color} />
                ) : null}
                <h1 className="font-medium leading-6 text-qb-black-100">
                    {currentPage?.name}
                </h1>
            </div>
            <div className="mt-4 sm:ml-16 sm:mt-0 flex flex-row gap-x-2">
                <ProfileMenu>
                    <span className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-gray-500">
                        <span className="text-sm font-medium leading-none text-white">
                            {initials}
                        </span>
                    </span>
                </ProfileMenu>
            </div>
        </div>
    );
}
