import { Fragment } from 'react';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { BarsArrowDownIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';

export type DropdownOption = {
    name: string;
    value: string;
};

type DropdownProps = {
    title?: string,
    icon?: React.ComponentType<{ className: string | undefined }>;
    options: DropdownOption[];
    selected: string | number;
    onChange: (value: string) => void;
    className?: string;
};

export default function Dropdown({
    title,
    icon: Icon,
    options,
    selected,
    onChange,
    className = '',
}: DropdownProps) {
    const selectedItem = options.find(a => a.value === selected)
    return (
        <Listbox value={selected} onChange={onChange}>
            {({ open }) => (
                <div className={classNames('relative inline-block text-left', className)}>
                    <ListboxButton className="flex items-center gap-1 rounded-md border border-gray-200 px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100">
                        {Icon && <Icon className="w-4 h-4 text-gray-400" />}
                        <span>{title}</span>
                        <span className="font-medium text-gray-900">{selectedItem?.name}</span>
                        <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                    </ListboxButton>

                    <Transition
                        as={Fragment}
                        show={open}
                        enter="transition ease-out duration-100"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <ListboxOptions className="absolute z-10 mt-1 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-sm">
                            {options.map((option) => (
                                <ListboxOption
                                    key={option.value}
                                    value={option.value}
                                    className={({ active }) =>
                                        classNames(
                                            active ? 'bg-gray-100' : '',
                                            'text-gray-900 cursor-default select-none px-4 py-2'
                                        )
                                    }
                                >
                                    {option.name}
                                </ListboxOption>
                            ))}
                        </ListboxOptions>
                    </Transition>
                </div>
            )}
        </Listbox>
    );
}
