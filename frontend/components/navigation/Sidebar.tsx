'use client';

import {
    ChevronDownIcon,
    ChevronUpIcon,
    MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import { useCommandPalette } from '@/components/CommandPalette';
import Stats from '@/components/navigation/Stats';
import { SidebarSections } from '@/util/pages';
import classNames from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Initials from '../Initials';
import CompanyPicker from './CompanyPicker';

function SidebarLink({
    name,
    icon: Icon,
    badge,
    href,
    color,
    isOpen,
    disabled
}: {
    name: string;
    icon?: React.ComponentType<{ className: string | undefined }> | null;
    badge?: string;
    href?: string | null;
    color?: string;
    isOpen?: boolean;
    disabled?: boolean;
}) {
    const path = usePathname();

    function selected(href: string): boolean {
        return path.startsWith(href);
    }

    const getIcon = () =>
        Icon ? <Icon className="text-qb-gray-150 h-5 w-5 shrink-0" /> : null;

    const getBadge = () =>
        badge !== undefined ? (
            <span
                aria-hidden="true"
                className="ml-auto w-9 min-w-max whitespace-nowrap rounded-full bg-white px-2.5 py-0.5 text-center text-xs font-medium leading-5 text-gray-600 ring-1 ring-inset ring-gray-200">
                {badge}
            </span>
        ) : null;

    if (!href) {
        return (
            <li>
                <span className="group flex gap-x-2 rounded-md  text-sm leading-6 items-center cursor-pointer select-none">
                    {getIcon()}
                    {!Icon ? <Initials name={name} color={color} /> : null}
                    {name}
                    {isOpen ? (
                        <ChevronUpIcon className="text-gray-400  h-3 w-3 shrink-0" />
                    ) : (
                        <ChevronDownIcon className="text-gray-400  h-3 w-3 shrink-0" />
                    )}
                    {getBadge()}
                </span>
            </li>
        );
    }

    return (
        <li>
            <Link
                href={href}
                className={classNames(
                    selected(href) ? 'bg-qb-gray-100' : 'hover:bg-qb-gray-100',
                    'group flex gap-x-2 rounded-md p-1 text-sm leading-6 items-center',
                    disabled ? 'opacity-50 cursor-default' : 'cursor-pointer'
                )}>
                {getIcon()}

                {!Icon ? <Initials name={name} color={color} /> : null}
                {name}
                {getBadge()}
            </Link>
        </li>
    );
}

export default function Sidebar({ sections }: { sections: SidebarSections }) {
    const commandPalette = useCommandPalette();
    const [openSection, setOpenSection] = useState<string | null>(null);

    const toggleSection = (sectionName: string) => {
        setOpenSection((prev) => (prev === sectionName ? null : sectionName));
    };

    return (
        <div className="h-screen w-72 bg-qb-gray-50 flex flex-col border-r border border-qb-gray-110 z-10 gap-y-3 selection-none">
            <div className="border-b border-gray-300 h-[60px]">
                <CompanyPicker />
            </div>
            <nav className="flex flex-1 flex-col px-4">
                <div className="mb-2 mt-4">
                    <Stats />
                </div>
                <div className="-mx-2 mt-4">
                    <label htmlFor="search" className="sr-only">
                        Search
                    </label>
                    <div onClick={() => commandPalette.open()} className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <MagnifyingGlassIcon
                                aria-hidden="true"
                                className="h-5 w-5 "
                            />
                        </div>
                        <div
                            id="query"
                            className="block w-full select-none rounded-lg border-0 cursor-text bg-white py-1 pl-10 pr-14 ring-1 ring-inset ring-gray-200 shadow-md focus:ring-inset focus:ring-gray-300 sm:text-sm sm:leading-6">
                            Search
                        </div>
                        <div className="absolute inset-y-0 right-0 flex py-1.5 pr-1.5">
                            <kbd className="inline-flex items-center rounded border border-gray-200 text-qb-gray-200 px-1 font-sans text-xs">
                                ⌘K
                            </kbd>
                        </div>
                    </div>
                </div>

                <ul
                    role="list"
                    className="flex flex-col flex-1 gap-y-1 mt-7 text-qb-black-100 font-medium">
                    {sections.map((section, index) => {
                        const isDataSection = section.name.toLowerCase() === 'data';
                        const isOpen = openSection === section.name;
                        return (
                            <li key={index}>
                                {isDataSection ? (
                                    <div className="!mt-[2rem]" />
                                ) : (
                                    <div
                                        onClick={() =>
                                            section.items.length &&
                                            toggleSection(section.name)
                                        }>
                                        <SidebarLink
                                            key={index}
                                            name={section.name}
                                            href={section.href}
                                            icon={section.icon}
                                            isOpen={isOpen}
                                        />
                                    </div>
                                )}
                                {section.items.length > 0 &&
                                    (isOpen || isDataSection) && (
                                        <ul
                                            role="list"
                                            className={`${!isDataSection ? 'ml-8' : 'flex flex-col gap-y-1 font-medium'}`}>
                                            {section.items.map((item, index) => (
                                                <SidebarLink
                                                    key={index}
                                                    name={item.name}
                                                    href={item.href}
                                                    icon={item.icon}
                                                    color={item.color}
                                                    badge={item.badge}
                                                    disabled={item.disabled}
                                                />
                                            ))}
                                        </ul>
                                    )}
                            </li>
                        );
                    })}
                </ul>
            </nav>
            <div className="border-t border-gray-300 px-4 py-2">
                <img src="/logo-grey.svg" alt="quarterback-logo" className="w-2/3" />
            </div>
        </div>
    );
}
