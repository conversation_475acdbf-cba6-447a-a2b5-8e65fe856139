import React from 'react';
import Initials, { InitialsProps } from '../Initials';
import classNames from 'classnames';

interface ContentCardProps {
    title: React.ReactNode;
    className?: string;
    childrenClassName?: string;
    initialBadge?: InitialsProps;
    children: React.ReactNode;
    loading?: boolean;
}

function ContentCard(props: ContentCardProps) {
    const {
        title,
        className = '',
        childrenClassName = '',
        initialBadge = {},
        children,
        loading = false
    } = props;

    return (
        <div className={classNames('bg-white border rounded-lg p-4', className)}>
            <div className="flex gap-x-2 divide-gray-200 items-center">
                {typeof title === 'string' ? (
                    <span className="bg-qb-gray-50 flex gap-2 p-2 rounded-md">
                        <Initials
                            name={initialBadge.name || title}
                            color={initialBadge.color}
                            hide={initialBadge.hide}
                        />
                        <span className="text-sm font-medium text-qb-black-100">
                            {title}
                        </span>
                    </span>
                ) : (
                    <div className="flex flex-col">
                        <span className="text-sm font-medium">{title}</span>
                    </div>
                )}
            </div>
            <div className="w-full mt-2 border-b border-gray-200"></div>
            {loading ? (
                <div className="w-full max-w-sm p-4 bg-white">
                    <div className="animate-pulse flex space-x-4">
                        <div className="flex-1 space-y-4 py-1">
                            <div className="h-4 bg-gray-200"></div>
                            <div className="space-y-2">
                                <div className="h-4 bg-gray-200"></div>
                                <div className="h-4 bg-gray-200  w-5/6"></div>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className={classNames('mt-4', childrenClassName)}>{children}</div>
            )}
        </div>
    );
}

export default ContentCard;
