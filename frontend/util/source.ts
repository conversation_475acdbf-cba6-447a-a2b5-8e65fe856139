import { Activity } from '@quarterback/types';

export default function source(activity: Activity): string {
    switch (activity.type) {
        case 'media':
            return activity.source.name;
        case 'tweet':
            return 'Twitter';
        case 'hotcopper':
            return 'Hotcopper';
        case 'asx-announcement':
            return 'ASX';
        case 'reddit':
        case 'redditComment':
            return 'Reddit';
        case 'linkedIn':
            return 'LinkedIn';
        case 'advfn':
            return 'ADVFN';
        case 'applepodcast':
            return 'Apple Podcasts';
        case 'audible':
            return 'Audible';
        case 'aussiestockforums':
            return 'Aussie Stock Forums';
        case 'castbox':
            return 'Castbox';
        case 'clubhouse':
            return 'Clubhouse';
        case 'iheartradio':
            return 'iHeartRadio';
        case 'investorhub':
            return 'InvestorHub';
        case 'medium':
            return 'Medium';
        case 'pinterest':
            return 'Pinterest';
        case 'quora':
            return 'Quora';
        case 'slack':
            return 'Slack';
        case 'snapchat':
            return 'Snapchat';
        case 'spotify':
            return 'Spotify';
        case 'stocktwits':
            return 'Stocktwits';
        case 'strawman':
            return 'Strawman';
        case 'tradingqna':
            return 'TradingQnA';
        case 'tumblr':
            return 'Tumblr';
        case 'vimeo':
            return 'Vimeo';
        case 'wechat':
            return 'WeChat';
        case 'whatsapp':
            return 'WhatsApp';
        case 'whirlpoolfinance':
            return 'Whirlpool Finance Forum';
        case 'youtube':
            return 'YouTube';
        case 'bogleheads':
            return 'Bogleheads Forum';
        case 'discord':
            return 'Discord';
        case 'telegram':
            return 'Telegram';
        case 'facebook':
            return 'Facebook';
        case 'instagram':
            return 'Instagram';
        case 'tiktok':
            return 'TikTok';
    }
}
