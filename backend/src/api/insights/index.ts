import { Response, Router } from 'express';
import OpenAI from 'openai';
import * as z from 'zod';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import db from '../../db/index.js';
import { read } from '../../db/repositories/activities.js';
import * as activitiesRepository from '../../db/repositories/activities/index.js';
import * as insightsRepository from '../../db/repositories/insights/index.js';

const openAi = new OpenAI();
const router: Router = Router({ mergeParams: true });

const QueryParams = z.object({
    from: z.coerce.date().optional(),
    to: z.coerce.date().optional(),
    organisation: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    ids: z.string().array().optional(),
    limit: z.coerce.number().optional(),
    type: z.string().optional().default('mixed'),
    chatter: z.coerce.boolean().optional(),
    extended: z.coerce.boolean().optional()
});

router.post('/', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, symbol, exchange, from, to, ids, limit, type, extended } =
        QueryParams.parse(request.body);

    const activities = await activitiesRepository.fetch(db, {
        symbol,
        exchange,
        from,
        to,
        ids
    });

    return response
        .status(200)
        .json(
            await insightsRepository.chatter(
                db,
                openAi,
                organisation,
                `${symbol}:${exchange}`,
                activities,
                limit,
                type,
                extended
            )
        );
});

router.post('/topics', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, symbol, exchange, from, to, ids } = QueryParams.parse(
        request.body
    );

    const activities = await activitiesRepository.fetch(db, {
        symbol,
        exchange,
        from,
        to,
        ids
    });

    return response
        .status(200)
        .json(
            await insightsRepository.topicsExtraction(
                db,
                openAi,
                organisation,
                `${symbol}:${exchange}`,
                activities
            )
        );
});

router.get('/wordcloud', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, symbol, exchange, from, to, ids, chatter } = QueryParams.parse(
        request.query
    );

    const activities = (
        await read(db, request.user!.sub!, organisation, symbol, exchange, {
            from,
            to,
            ids
        })
    ).filter((activity) =>
        chatter ? 'isBroadcast' in activity && !activity.isBroadcast : true
    );

    return response.status(200).json(await insightsRepository.wordCloud(activities));
});

export default router;
