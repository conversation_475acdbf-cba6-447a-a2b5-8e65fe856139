import {
    boolean,
    doublePrecision,
    foreignKey,
    index,
    integer,
    json,
    pgTable,
    primaryKey,
    text,
    timestamp,
    unique,
    varchar,
    pgEnum
} from 'drizzle-orm/pg-core';

import { listedEntity } from './market.js';
import { organisation } from './organisation.js';

export const asxAnnouncement = pgTable(
    'ASXAnnouncement',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),

        title: varchar('title', { length: 256 }).notNull(),
        priceSensitive: boolean('priceSensitive').notNull(),
        body: text('body'),
        isBroadcast: boolean('isBroadcast').notNull().default(true)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        })
    })
);

export const newsSource = pgTable('NewsSource', {
    url: varchar('url', { length: 1024 }).primaryKey(),
    name: varchar('name', { length: 256 }).notNull(),
    logo: varchar('logo', { length: 1024 })
});

export const socialSource = pgTable('SocialSource', {
    url: varchar('url', { length: 1024 }).primaryKey(),
    name: varchar('name', { length: 256 }).notNull(),
    logo: varchar('logo', { length: 1024 })
});

export const newsArticle = pgTable(
    'NewsArticle',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),

        title: varchar('title', { length: 256 }).notNull(),
        summary: text('summary'),
        image: varchar('image', { length: 1024 }),

        source: varchar('source', { length: 1024 })
    },
    (table) => ({
        newsSource: foreignKey({
            columns: [table.source],
            foreignColumns: [newsSource.url]
        }),
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade')
    })
);

export const hotCopperThread = pgTable('HotCopperThread', {
    thread: varchar('thread', { length: 128 }).primaryKey(),
    title: varchar('title', { length: 256 }).notNull(),
    views: integer('views').notNull()
});

export const hotCopperPost = pgTable(
    'HotCopperPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        post: integer('post').unique().notNull(),

        body: text('body').notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        likes: integer('likes').notNull(),

        sentiment: varchar('sentiment', { length: 64 }),
        disclosure: varchar('disclosure', { length: 64 }),

        thread: varchar('thread', { length: 128 }).notNull(),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        thread: foreignKey({
            columns: [table.thread],
            foreignColumns: [hotCopperThread.thread]
        }),
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const twitterPost = pgTable(
    'TwitterPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        views: integer('views').default(0).notNull(),
        likes: integer('likes').default(0).notNull(),
        thread: varchar('thread', { length: 128 }),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade'),
        threadIndex: index('thread_idx').on(table.thread)
    })
);

export const redditPost = pgTable(
    'RedditPost',
    {
        id: varchar('id', { length: 64 }).notNull(),
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 1024 }).notNull(),
        body: text('body').notNull(),
        score: integer('score').notNull(),
        ratio: doublePrecision('ratio').notNull(),
        subreddit: varchar('subreddit', { length: 64 }),
        user: varchar('user', { length: 128 }).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const linkedInPost = pgTable(
    'LinkedInPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: text('title'),
        userId: varchar('userId', { length: 128 }),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull().default(''),
        image: varchar('image', { length: 1024 }),
        comments: integer('comments').default(0).notNull(),
        likes: integer('likes').default(0).notNull(),
        thread: varchar('thread', { length: 128 }),
        commentUrn: text('commentUrn'),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const linkedInCookies = pgTable('LinkedInCookies', {
    email: varchar('email', { length: 256 }).primaryKey(),
    cookie: json('cookie').notNull(),
    lastUsed: timestamp('lastUsed', { withTimezone: true })
});

export const redditComment = pgTable(
    'RedditComment',
    {
        id: varchar('id', { length: 64 }).notNull(),
        activity: varchar('activity', { length: 32 }).notNull(),
        parent: varchar('parent', { length: 64 }).notNull(),
        body: text('body').notNull(),
        score: integer('score').notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        post: varchar('post', { length: 64 })
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const advfnPost = pgTable(
    'ADVFNPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const applePodcastPost = pgTable(
    'ApplePodcastPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const audiblePost = pgTable(
    'AudiblePost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const aussiestockforumsPost = pgTable(
    'AussieStockForums',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const castboxPost = pgTable(
    'CastboxPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const clubhousePost = pgTable(
    'ClubhousePost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const discordPost = pgTable(
    'DiscordPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const facebookPost = pgTable(
    'FacebookPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const iheartradioPost = pgTable(
    'iHeartRadioPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const instagramPost = pgTable(
    'InstagramPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const investorhubPost = pgTable(
    'InvestorHubPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const mediumPost = pgTable(
    'MediumPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const pinterestPost = pgTable(
    'PinterestPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const quoraPost = pgTable(
    'QuoraPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const slackPost = pgTable(
    'SlackPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const snapchatPost = pgTable(
    'SnapchatPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const spotifyPost = pgTable(
    'SpotifyPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const stocktwitsPost = pgTable(
    'StocktwitsPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const strawmanPost = pgTable(
    'StrawmanPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const telegramPost = pgTable(
    'TelegramPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const tiktokPost = pgTable(
    'TikTokPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const tradingqnaPost = pgTable(
    'TradingQnAPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const tumblrPost = pgTable(
    'TumblrPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const vimeoPost = pgTable(
    'VimeoPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const wechatPost = pgTable(
    'WeChatPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const whatsappPost = pgTable(
    'WhatsAppPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const whirlpoolFinancePost = pgTable(
    'WhirlpoolFinancePost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const youtubePost = pgTable(
    'YouTubePost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        title: varchar('title', { length: 256 }).notNull(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const bogleheadsPost = pgTable(
    'BogleHeads',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }).notNull(),
        body: text('body').notNull(),
        likes: integer('likes').default(0).notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const callPost = pgTable(
    'CallPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        body: text('body').notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const meetingPost = pgTable(
    'MeetingPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }),
        body: text('body').notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const eventPost = pgTable(
    'EventPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }),
        body: text('body').notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const presentationPost = pgTable(
    'PresentationPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }),
        body: text('body').notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const otherPost = pgTable(
    'OtherPost',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        user: varchar('user', { length: 128 }).notNull(),
        title: varchar('title', { length: 256 }),
        body: text('body').notNull(),
        image: varchar('image', { length: 1024 }),
        isBroadcast: boolean('isBroadcast').notNull().default(false)
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        user: foreignKey({
            columns: [table.user],
            foreignColumns: [author.key]
        }).onDelete('cascade')
    })
);

export const author = pgTable('Author', {
    key: varchar('key', { length: 1024 }).primaryKey(),
    userId: varchar('userId', { length: 128 }).notNull(),
    name: varchar('name', { length: 256 }),
    image: varchar('image', { length: 1024 }),
    url: varchar('url', { length: 1024 }),
    followers: integer('followers'),
    following: integer('following')
});

export const sentiment = pgTable(
    'ActivitySentiment',
    {
        activity: varchar('activity', { length: 32 }).primaryKey(),
        magnitude: doublePrecision('magnitude').notNull(),
        score: doublePrecision('score').notNull(),
        reason: text('reason')
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade')
    })
);

export const activityFormatEnum = pgEnum('format', [
    'chatter',
    'broadcast',
    'media',
    'announcement',
    'call',
    'meeting',
    'presentation',
    'event',
    'other'
]);

export const activity = pgTable(
    'Activity',
    {
        id: varchar('id', { length: 32 }).primaryKey(),
        key: varchar('key', { length: 1024 }).notNull(),
        posted: timestamp('posted', { withTimezone: true }).notNull(),
        url: varchar('url', { length: 1024 }).notNull(),
        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull(),
        format: activityFormatEnum('format')
    },
    (table) => ({
        key: unique().on(table.key, table.symbol, table.exchange),
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        })
    })
);

export const read = pgTable(
    'ActivityRead',
    {
        activity: varchar('activity', { length: 32 }).notNull(),
        user: varchar('user', { length: 36 }).notNull(),
        at: timestamp('at', { withTimezone: true }).notNull().defaultNow()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.activity, table.user] }),
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade')
    })
);

export const archived = pgTable(
    'ActivityArchived',
    {
        activity: varchar('activity', { length: 32 }).notNull(),
        organisation: varchar('organisation', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.activity, table.organisation] }),
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        })
    })
);

export const flagged = pgTable(
    'ActivityFlagged',
    {
        activity: varchar('activity', { length: 32 }).notNull(),
        organisation: varchar('organisation', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.activity, table.organisation] }),
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        })
    })
);

export const summary = pgTable(
    'ActivitySummary',
    {
        activity: varchar('activity', { length: 32 }).notNull().primaryKey(),
        summary: text('summary').notNull(),
        at: timestamp('at', { withTimezone: true }).notNull().defaultNow()
    },
    (table) => ({
        activity: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade')
    })
);

export const activityFiles = pgTable(
    'ActivityFiles',
    {
        storagePath: varchar('storagePath', { length: 512 }).notNull().primaryKey(),
        activity: varchar('activity', { length: 32 }).notNull(),
        fileName: varchar('fileName', { length: 256 }).notNull(),
        fileSize: integer('fileSize').notNull(),
        fileType: varchar('fileType', { length: 128 }).notNull(),
        uploadedAt: timestamp('uploadedAt', { withTimezone: true }).notNull().defaultNow()
    },
    (table) => ({
        activityFk: foreignKey({
            columns: [table.activity],
            foreignColumns: [activity.id]
        }).onDelete('cascade'),
        activityIdx: index('ActivityFiles_activity_idx').on(table.activity)
    })
);
