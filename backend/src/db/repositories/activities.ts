import { AnyPgColumn, PgDatabase, integer } from 'drizzle-orm/pg-core';
import { Activity, HotCopperDisclosure, HotCopperSentiment } from '@quarterback/types';
import {
    activity,
    asxAnnouncement,
    hotCopperPost,
    hotCopperThread,
    newsArticle,
    newsSource,
    read as activityRead,
    archived as activityArchived,
    flagged as activityFlagged,
    sentiment as activitySentiment,
    twitterPost,
    redditPost,
    linkedInPost,
    redditComment,
    author,
    advfnPost,
    applePodcastPost,
    audiblePost,
    aussiestockforumsPost,
    castboxPost,
    clubhousePost,
    discordPost,
    facebookPost,
    iheartradioPost,
    instagramPost,
    investorhubPost,
    mediumPost,
    pinterestPost,
    quoraPost,
    slackPost,
    snapchatPost,
    spotifyPost,
    stocktwitsPost,
    strawmanPost,
    telegramPost,
    tiktokPost,
    tradingqnaPost,
    tumblrPost,
    vimeoPost,
    wechatPost,
    whatsappPost,
    whirlpoolFinancePost,
    youtubePost,
    bogleheadsPost
} from '../schema/activities.js';
import {
    and,
    count as drizzleCount,
    desc,
    eq,
    exists,
    gte,
    inArray,
    isNotNull,
    isNull,
    lt,
    not,
    or,
    SQL,
    sql
} from 'drizzle-orm';
import { isDefined } from '@quarterback/util';
import z from 'zod';
import { listedEntity } from '../schema/market.js';

/*
if broadcast
    - exists an asx announcement
    - exists a tweet with user of org
    - exists a hotcopper post with user asx news
if chatter
    - exists a news post
    - exists a tweet not matching entity user
    - exists a hotcopper post not matching asx news user
 */

export function lower(email: AnyPgColumn): SQL {
    return sql`lower(${email})`;
}

function provenanceFilter(
    db: PgDatabase<any>,
    provenance: 'broadcast' | 'chatter',
    twitterUsername: string | null,
    linkedInUsername: string | null
) {
    switch (provenance) {
        case 'broadcast':
            return or(
                exists(
                    db
                        .select()
                        .from(asxAnnouncement)
                        .where(eq(asxAnnouncement.activity, activity.id))
                ),
                exists(
                    db
                        .select()
                        .from(hotCopperPost)
                        .where(
                            and(
                                eq(hotCopperPost.activity, activity.id),
                                eq(hotCopperPost.user, 'ASX News')
                            )
                        )
                ),
                twitterUsername
                    ? exists(
                          db
                              .select()
                              .from(twitterPost)
                              .where(
                                  and(
                                      eq(twitterPost.activity, activity.id),
                                      eq(
                                          lower(twitterPost.user),
                                          twitterUsername.toLowerCase()
                                      )
                                  )
                              )
                      )
                    : undefined,
                linkedInUsername
                    ? exists(
                          db
                              .select()
                              .from(linkedInPost)
                              .where(
                                  and(
                                      eq(linkedInPost.activity, activity.id),
                                      eq(
                                          lower(linkedInPost.userId),
                                          linkedInUsername.toLowerCase()
                                      )
                                  )
                              )
                      )
                    : undefined
            );
        case 'chatter':
            return or(
                exists(
                    db
                        .select()
                        .from(newsArticle)
                        .where(eq(newsArticle.activity, activity.id))
                ),
                exists(
                    db
                        .select()
                        .from(hotCopperPost)
                        .where(
                            and(
                                eq(hotCopperPost.activity, activity.id),
                                not(eq(hotCopperPost.user, 'ASX News'))
                            )
                        )
                ),
                exists(
                    db
                        .select()
                        .from(twitterPost)
                        .where(
                            and(
                                eq(twitterPost.activity, activity.id),
                                twitterUsername
                                    ? not(
                                          eq(
                                              lower(twitterPost.user),
                                              twitterUsername.toLowerCase()
                                          )
                                      )
                                    : undefined
                            )
                        )
                ),
                exists(
                    db
                        .select()
                        .from(redditPost)
                        .where(eq(redditPost.activity, activity.id))
                )
            );
    }
}

export const ActivityCountOptions = z.object({
    read: z.boolean().optional(),
    provenance: z.enum(['broadcast', 'chatter']).optional()
});
export type ActivityCountOptions = z.infer<typeof ActivityCountOptions>;

export async function count(
    db: PgDatabase<any>,
    user: string,
    symbol: string,
    exchange: string,
    options: ActivityCountOptions
) {
    const [{ twitterUsername, linkedInUsername }] = await db
        .select({
            twitterUsername: listedEntity.twitterUsername,
            linkedInUsername: listedEntity.linkedInUsername
        })
        .from(listedEntity)
        .where(and(eq(listedEntity.symbol, symbol), eq(listedEntity.exchange, exchange)));

    const [{ entries }] = await db
        .select({ entries: drizzleCount(activity.id) })
        .from(activity)
        .leftJoin(
            activityRead,
            and(eq(activityRead.activity, activity.id), eq(activityRead.user, user))
        )
        .where(
            and(
                eq(activity.symbol, symbol),
                eq(activity.exchange, exchange),
                options.read !== undefined
                    ? options.read
                        ? isNotNull(activityRead.activity)
                        : isNull(activityRead.activity)
                    : undefined,
                options.provenance !== undefined
                    ? provenanceFilter(
                          db,
                          options.provenance,
                          twitterUsername,
                          linkedInUsername
                      )
                    : undefined
            )
        );

    return entries ?? 0;
}

function sourceFilter(source: 'asx') {
    if (source === 'asx') {
        return isNotNull(asxAnnouncement.activity);
    } else {
        return undefined;
    }
}

export const ActivityReadOptions = z.union([
    z.object({ id: z.string() }),
    z.object({
        from: z.date().optional(),
        to: z.date().optional(),
        read: z.boolean().optional(),
        limit: z.number().optional(),
        archived: z.boolean().optional(),
        flagged: z.boolean().optional(),
        thread: z.string().optional(),
        ids: z.array(z.string()).optional(),
        source: z.enum(['asx']).optional()
    })
]);
export type ActivityReadOptions = z.infer<typeof ActivityReadOptions>;

/** @deprecated */
export async function read(
    db: PgDatabase<any>,
    user: string,
    organisation: string,
    symbol: string,
    exchange: string,
    options: ActivityReadOptions
): Promise<Array<Activity>> {
    let query = db
        .select()
        .from(activity)
        .leftJoin(
            activityRead,
            and(eq(activityRead.activity, activity.id), eq(activityRead.user, user))
        )
        .leftJoin(
            activityArchived,
            and(
                eq(activityArchived.activity, activity.id),
                eq(activityArchived.organisation, organisation)
            )
        )
        .leftJoin(
            activityFlagged,
            and(
                eq(activityFlagged.activity, activity.id),
                eq(activityFlagged.organisation, organisation)
            )
        )
        .leftJoin(activitySentiment, eq(activity.id, activitySentiment.activity))
        .leftJoin(asxAnnouncement, eq(activity.id, asxAnnouncement.activity))
        .leftJoin(newsArticle, eq(activity.id, newsArticle.activity))
        .leftJoin(newsSource, eq(newsArticle.source, newsSource.url))
        .leftJoin(hotCopperPost, eq(activity.id, hotCopperPost.activity))
        .leftJoin(hotCopperThread, eq(hotCopperPost.thread, hotCopperThread.thread))
        .leftJoin(twitterPost, eq(activity.id, twitterPost.activity))
        .leftJoin(redditPost, eq(activity.id, redditPost.activity))
        .leftJoin(redditComment, eq(activity.id, redditComment.activity))
        .leftJoin(linkedInPost, eq(activity.id, linkedInPost.activity))
        .leftJoin(advfnPost, eq(activity.id, advfnPost.activity))
        .leftJoin(applePodcastPost, eq(activity.id, applePodcastPost.activity))
        .leftJoin(audiblePost, eq(activity.id, audiblePost.activity))
        .leftJoin(aussiestockforumsPost, eq(activity.id, aussiestockforumsPost.activity))
        .leftJoin(castboxPost, eq(activity.id, castboxPost.activity))
        .leftJoin(clubhousePost, eq(activity.id, clubhousePost.activity))
        .leftJoin(discordPost, eq(activity.id, discordPost.activity))
        .leftJoin(facebookPost, eq(activity.id, facebookPost.activity))
        .leftJoin(iheartradioPost, eq(activity.id, iheartradioPost.activity))
        .leftJoin(instagramPost, eq(activity.id, instagramPost.activity))
        .leftJoin(investorhubPost, eq(activity.id, investorhubPost.activity))
        .leftJoin(mediumPost, eq(activity.id, mediumPost.activity))
        .leftJoin(pinterestPost, eq(activity.id, pinterestPost.activity))
        .leftJoin(quoraPost, eq(activity.id, quoraPost.activity))
        .leftJoin(slackPost, eq(activity.id, slackPost.activity))
        .leftJoin(snapchatPost, eq(activity.id, snapchatPost.activity))
        .leftJoin(spotifyPost, eq(activity.id, spotifyPost.activity))
        .leftJoin(stocktwitsPost, eq(activity.id, stocktwitsPost.activity))
        .leftJoin(strawmanPost, eq(activity.id, strawmanPost.activity))
        .leftJoin(telegramPost, eq(activity.id, telegramPost.activity))
        .leftJoin(tiktokPost, eq(activity.id, tiktokPost.activity))
        .leftJoin(tradingqnaPost, eq(activity.id, tradingqnaPost.activity))
        .leftJoin(tumblrPost, eq(activity.id, tumblrPost.activity))
        .leftJoin(vimeoPost, eq(activity.id, vimeoPost.activity))
        .leftJoin(wechatPost, eq(activity.id, wechatPost.activity))
        .leftJoin(whatsappPost, eq(activity.id, whatsappPost.activity))
        .leftJoin(whirlpoolFinancePost, eq(activity.id, whirlpoolFinancePost.activity))
        .leftJoin(youtubePost, eq(activity.id, youtubePost.activity))
        .leftJoin(bogleheadsPost, eq(activity.id, bogleheadsPost.activity))

        .leftJoin(
            author,
            or(
                eq(twitterPost.user, author.key),
                eq(linkedInPost.user, author.key),
                eq(redditPost.user, author.key),
                eq(redditComment.user, author.key),
                eq(hotCopperPost.user, author.key),
                eq(advfnPost.user, author.key),
                eq(applePodcastPost.user, author.key),
                eq(audiblePost.user, author.key),
                eq(aussiestockforumsPost.user, author.key),
                eq(castboxPost.user, author.key),
                eq(clubhousePost.user, author.key),
                eq(discordPost.user, author.key),
                eq(facebookPost.user, author.key),
                eq(iheartradioPost.user, author.key),
                eq(instagramPost.user, author.key),
                eq(investorhubPost.user, author.key),
                eq(mediumPost.user, author.key),
                eq(pinterestPost.user, author.key),
                eq(quoraPost.user, author.key),
                eq(slackPost.user, author.key),
                eq(snapchatPost.user, author.key),
                eq(spotifyPost.user, author.key),
                eq(stocktwitsPost.user, author.key),
                eq(strawmanPost.user, author.key),
                eq(telegramPost.user, author.key),
                eq(tiktokPost.user, author.key),
                eq(tradingqnaPost.user, author.key),
                eq(tumblrPost.user, author.key),
                eq(vimeoPost.user, author.key),
                eq(wechatPost.user, author.key),
                eq(whatsappPost.user, author.key),
                eq(whirlpoolFinancePost.user, author.key),
                eq(youtubePost.user, author.key),
                eq(bogleheadsPost.user, author.key)
            )
        )
        .where(
            and(
                'id' in options
                    ? eq(activity.id, options.id)
                    : and(
                          eq(activity.symbol, symbol),
                          eq(activity.exchange, exchange),

                          options.from && gte(activity.posted, options.from),
                          options.to && lt(activity.posted, options.to),

                          options.ids !== undefined
                              ? inArray(activity.id, options.ids)
                              : undefined,

                          options.read !== undefined
                              ? options.read
                                  ? isNotNull(activityRead.activity)
                                  : isNull(activityRead.activity)
                              : undefined,

                          !options?.thread
                              ? options?.archived
                                  ? isNotNull(activityArchived.activity)
                                  : isNull(activityArchived.activity)
                              : undefined,

                          options.flagged !== undefined
                              ? options.flagged
                                  ? isNotNull(activityFlagged.activity)
                                  : isNull(activityFlagged.activity)
                              : undefined,

                          options.thread !== undefined
                              ? or(
                                    eq(twitterPost.thread, options.thread),
                                    eq(linkedInPost.thread, options.thread),
                                    eq(hotCopperThread.thread, options.thread),
                                    eq(redditPost.id, options.thread),
                                    eq(redditComment.post, options.thread)
                                )
                              : undefined,

                          options.source !== undefined
                              ? sourceFilter(options.source)
                              : undefined
                      )
            )
        )
        .orderBy(desc(activity.posted))
        .$dynamic();

    if (!('id' in options) && options.limit) {
        query = query.limit(options.limit!);
    }

    return (await query)
        .map((activity) => {
            const base = {
                id: activity.Activity.id,
                posted: activity.Activity.posted,
                read: activity.ActivityRead !== null,
                archived: activity.ActivityArchived !== null,
                flagged: activity.ActivityFlagged !== null,
                url: activity.Activity.url,
                author: activity.Author
                    ? {
                          key: activity.Author?.key!,
                          userId: activity.Author.userId!,
                          name: activity.Author?.name!,
                          image: activity.Author?.image,
                          url: activity.Author?.url,
                          followers: activity.Author?.followers,
                          following: activity.Author?.following
                      }
                    : undefined
            };

            if (activity.ASXAnnouncement) {
                return {
                    ...base,
                    type: 'asx-announcement' as const,
                    title: activity.ASXAnnouncement.title,
                    priceSensitive: activity.ASXAnnouncement.priceSensitive,
                    pdf: activity.Activity.url,
                    isBroadcast: activity.ASXAnnouncement.isBroadcast
                };
            }

            if (activity.NewsArticle && activity.NewsSource) {
                return {
                    ...base,
                    type: 'media' as const,
                    title: activity.NewsArticle.title,
                    image: activity.NewsArticle.image,
                    summary: activity.NewsArticle.summary,
                    source: activity.NewsSource,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.HotCopperPost && activity.HotCopperThread) {
                return {
                    ...base,
                    type: 'hotcopper' as const,
                    title: activity.HotCopperThread.title,
                    description: activity.HotCopperPost.body,
                    thread: {
                        views: activity.HotCopperThread.views,
                        thread: activity.HotCopperThread.thread
                    },
                    post: activity.HotCopperPost.post,
                    hotcopper: {
                        sentiment: (activity.HotCopperPost.sentiment ??
                            'NONE') as HotCopperSentiment,
                        disclosure: (activity.HotCopperPost.disclosure ??
                            'UNDISCLOSED') as HotCopperDisclosure
                    },
                    likes: activity.HotCopperPost.likes,
                    isBroadcast: activity.HotCopperPost.isBroadcast,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.TwitterPost) {
                return {
                    ...base,
                    type: 'tweet' as const,
                    body: activity.TwitterPost.body,
                    views: activity.TwitterPost.views,
                    likes: activity.TwitterPost.likes,
                    image: activity.TwitterPost.image,
                    thread: activity.TwitterPost.thread,
                    isBroadcast: activity.TwitterPost.isBroadcast,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.RedditPost) {
                return {
                    ...base,
                    type: 'reddit' as const,
                    title: activity.RedditPost.title,
                    body: activity.RedditPost.body,
                    score: activity.RedditPost.score,
                    ratio: activity.RedditPost.ratio,
                    image: activity.RedditPost.image,
                    post: activity.RedditPost.id,
                    isBroadcast: activity.RedditPost.isBroadcast,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.LinkedInPost) {
                return {
                    ...base,
                    type: 'linkedIn' as const,
                    title: activity.LinkedInPost.title,
                    body: activity.LinkedInPost.body,
                    image: activity.LinkedInPost.image,
                    userId: activity.LinkedInPost.userId,
                    comments: activity.LinkedInPost.comments,
                    likes: activity.LinkedInPost.likes,
                    thread: activity.LinkedInPost.thread,
                    isBroadcast: activity.LinkedInPost.isBroadcast,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.RedditComment) {
                return {
                    ...base,
                    type: 'redditComment' as const,
                    post: activity.RedditComment.post || undefined,
                    parent: activity.RedditComment.parent,
                    score: activity.RedditComment.score,
                    body: activity.RedditComment.body,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.ADVFNPost) {
                return {
                    ...base,
                    type: 'advfn' as const,
                    title: activity.ADVFNPost.title,
                    body: activity.ADVFNPost.body,
                    isBroadcast: activity.ADVFNPost.isBroadcast,
                    likes: activity.ADVFNPost.likes,
                    image: activity.ADVFNPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.ApplePodcastPost) {
                return {
                    ...base,
                    type: 'applepodcast' as const,
                    title: activity.ApplePodcastPost.title,
                    body: activity.ApplePodcastPost.body,
                    isBroadcast: activity.ApplePodcastPost.isBroadcast,
                    likes: activity.ApplePodcastPost.likes,
                    image: activity.ApplePodcastPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.AudiblePost) {
                return {
                    ...base,
                    type: 'audible' as const,
                    title: activity.AudiblePost.title,
                    body: activity.AudiblePost.body,
                    isBroadcast: activity.AudiblePost.isBroadcast,
                    likes: activity.AudiblePost.likes,
                    image: activity.AudiblePost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.AussieStockForums) {
                return {
                    ...base,
                    type: 'aussiestockforums' as const,
                    title: activity.AussieStockForums.title,
                    body: activity.AussieStockForums.body,
                    isBroadcast: activity.AussieStockForums.isBroadcast,
                    likes: activity.AussieStockForums.likes,
                    image: activity.AussieStockForums.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.CastboxPost) {
                return {
                    ...base,
                    type: 'castbox' as const,
                    title: activity.CastboxPost.title,
                    body: activity.CastboxPost.body,
                    isBroadcast: activity.CastboxPost.isBroadcast,
                    likes: activity.CastboxPost.likes,
                    image: activity.CastboxPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.ClubhousePost) {
                return {
                    ...base,
                    type: 'clubhouse' as const,
                    title: activity.ClubhousePost.title,
                    body: activity.ClubhousePost.body,
                    isBroadcast: activity.ClubhousePost.isBroadcast,
                    likes: activity.ClubhousePost.likes,
                    image: activity.ClubhousePost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.DiscordPost) {
                return {
                    ...base,
                    type: 'discord' as const,
                    body: activity.DiscordPost.body,
                    isBroadcast: activity.DiscordPost.isBroadcast,
                    likes: activity.DiscordPost.likes,
                    image: activity.DiscordPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.FacebookPost) {
                return {
                    ...base,
                    type: 'facebook' as const,
                    body: activity.FacebookPost.body,
                    isBroadcast: activity.FacebookPost.isBroadcast,
                    likes: activity.FacebookPost.likes,
                    image: activity.FacebookPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.iHeartRadioPost) {
                return {
                    ...base,
                    type: 'iheartradio' as const,
                    title: activity.iHeartRadioPost.title,
                    body: activity.iHeartRadioPost.body,
                    isBroadcast: activity.iHeartRadioPost.isBroadcast,
                    likes: activity.iHeartRadioPost.likes,
                    image: activity.iHeartRadioPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.InstagramPost) {
                return {
                    ...base,
                    type: 'instagram' as const,
                    body: activity.InstagramPost.body,
                    isBroadcast: activity.InstagramPost.isBroadcast,
                    likes: activity.InstagramPost.likes,
                    image: activity.InstagramPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.InvestorHubPost) {
                return {
                    ...base,
                    type: 'investorhub' as const,
                    title: activity.InvestorHubPost.title,
                    body: activity.InvestorHubPost.body,
                    isBroadcast: activity.InvestorHubPost.isBroadcast,
                    likes: activity.InvestorHubPost.likes,
                    image: activity.InvestorHubPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.MediumPost) {
                return {
                    ...base,
                    type: 'medium' as const,
                    title: activity.MediumPost.title,
                    body: activity.MediumPost.body,
                    isBroadcast: activity.MediumPost.isBroadcast,
                    likes: activity.MediumPost.likes,
                    image: activity.MediumPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.PinterestPost) {
                return {
                    ...base,
                    type: 'pinterest' as const,
                    title: activity.PinterestPost.title,
                    body: activity.PinterestPost.body,
                    isBroadcast: activity.PinterestPost.isBroadcast,
                    likes: activity.PinterestPost.likes,
                    image: activity.PinterestPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.QuoraPost) {
                return {
                    ...base,
                    type: 'quora' as const,
                    title: activity.QuoraPost.title,
                    body: activity.QuoraPost.body,
                    isBroadcast: activity.QuoraPost.isBroadcast,
                    likes: activity.QuoraPost.likes,
                    image: activity.QuoraPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.SlackPost) {
                return {
                    ...base,
                    type: 'slack' as const,
                    title: activity.SlackPost.title,
                    body: activity.SlackPost.body,
                    isBroadcast: activity.SlackPost.isBroadcast,
                    likes: activity.SlackPost.likes,
                    image: activity.SlackPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.SnapchatPost) {
                return {
                    ...base,
                    type: 'snapchat' as const,
                    body: activity.SnapchatPost.body,
                    isBroadcast: activity.SnapchatPost.isBroadcast,
                    likes: activity.SnapchatPost.likes,
                    image: activity.SnapchatPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.SpotifyPost) {
                return {
                    ...base,
                    type: 'spotify' as const,
                    title: activity.SpotifyPost.title,
                    body: activity.SpotifyPost.body,
                    isBroadcast: activity.SpotifyPost.isBroadcast,
                    likes: activity.SpotifyPost.likes,
                    image: activity.SpotifyPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.StocktwitsPost) {
                return {
                    ...base,
                    type: 'stocktwits' as const,
                    body: activity.StocktwitsPost.body,
                    isBroadcast: activity.StocktwitsPost.isBroadcast,
                    likes: activity.StocktwitsPost.likes,
                    image: activity.StocktwitsPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.StrawmanPost) {
                return {
                    ...base,
                    type: 'strawman' as const,
                    title: activity.StrawmanPost.title,
                    body: activity.StrawmanPost.body,
                    isBroadcast: activity.StrawmanPost.isBroadcast,
                    likes: activity.StrawmanPost.likes,
                    image: activity.StrawmanPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.TelegramPost) {
                return {
                    ...base,
                    type: 'telegram' as const,
                    body: activity.TelegramPost.body,
                    isBroadcast: activity.TelegramPost.isBroadcast,
                    likes: activity.TelegramPost.likes,
                    image: activity.TelegramPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.TikTokPost) {
                return {
                    ...base,
                    type: 'tiktok' as const,
                    body: activity.TikTokPost.body,
                    isBroadcast: activity.TikTokPost.isBroadcast,
                    likes: activity.TikTokPost.likes,
                    image: activity.TikTokPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.TradingQnAPost) {
                return {
                    ...base,
                    type: 'tradingqna' as const,
                    title: activity.TradingQnAPost.title,
                    body: activity.TradingQnAPost.body,
                    isBroadcast: activity.TradingQnAPost.isBroadcast,
                    likes: activity.TradingQnAPost.likes,
                    image: activity.TradingQnAPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.TumblrPost) {
                return {
                    ...base,
                    type: 'tumblr' as const,
                    title: activity.TumblrPost.title,
                    body: activity.TumblrPost.body,
                    isBroadcast: activity.TumblrPost.isBroadcast,
                    likes: activity.TumblrPost.likes,
                    image: activity.TumblrPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.VimeoPost) {
                return {
                    ...base,
                    type: 'vimeo' as const,
                    title: activity.VimeoPost.title,
                    body: activity.VimeoPost.body,
                    isBroadcast: activity.VimeoPost.isBroadcast,
                    likes: activity.VimeoPost.likes,
                    image: activity.VimeoPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.WeChatPost) {
                return {
                    ...base,
                    type: 'wechat' as const,
                    title: activity.WeChatPost.title,
                    body: activity.WeChatPost.body,
                    isBroadcast: activity.WeChatPost.isBroadcast,
                    likes: activity.WeChatPost.likes,
                    image: activity.WeChatPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.WhatsAppPost) {
                return {
                    ...base,
                    type: 'whatsapp' as const,
                    body: activity.WhatsAppPost.body,
                    isBroadcast: activity.WhatsAppPost.isBroadcast,
                    likes: activity.WhatsAppPost.likes,
                    image: activity.WhatsAppPost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.WhirlpoolFinancePost) {
                return {
                    ...base,
                    type: 'whirlpoolfinance' as const,
                    title: activity.WhirlpoolFinancePost.title,
                    body: activity.WhirlpoolFinancePost.body,
                    isBroadcast: activity.WhirlpoolFinancePost.isBroadcast,
                    likes: activity.WhirlpoolFinancePost.likes,
                    image: activity.WhirlpoolFinancePost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.YouTubePost) {
                return {
                    ...base,
                    type: 'youtube' as const,
                    title: activity.YouTubePost.title,
                    body: activity.YouTubePost.body,
                    isBroadcast: activity.YouTubePost.isBroadcast,
                    likes: activity.YouTubePost.likes,
                    image: activity.YouTubePost.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }

            if (activity.BogleHeads) {
                return {
                    ...base,
                    type: 'bogleheads' as const,
                    title: activity.BogleHeads.title,
                    body: activity.BogleHeads.body,
                    isBroadcast: activity.BogleHeads.isBroadcast,
                    likes: activity.BogleHeads.likes,
                    image: activity.BogleHeads.image,
                    sentiment: activity.ActivitySentiment
                        ? {
                              score: activity.ActivitySentiment.score,
                              magnitude: activity.ActivitySentiment.magnitude
                          }
                        : undefined
                };
            }
        })
        .filter(isDefined);
}
